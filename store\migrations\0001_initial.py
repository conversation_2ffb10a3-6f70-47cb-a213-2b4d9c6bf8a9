# Generated by Django 5.2 on 2025-05-16 15:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Store',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('store_name', models.CharField(max_length=100)),
                ('address', models.CharField(blank=True, max_length=255)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('is_active', models.Bo<PERSON>anField(default=True)),
            ],
        ),
    ]
