# Generated by Django 5.2 on 2025-08-12 00:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('store', '0004_remove_store_latitude_remove_store_longitude_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='store',
            name='branch_head',
            field=models.OneToOneField(blank=True, limit_choices_to={'deleted': False, 'is_active': True, 'role': 'branch_head'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_store', to=settings.AUTH_USER_MODEL),
        ),
    ]
