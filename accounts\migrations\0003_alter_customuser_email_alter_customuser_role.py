# Generated by Django 5.2 on 2025-05-20 17:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_customuser_store_alter_customuser_role'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='customuser',
            name='email',
            field=models.EmailField(max_length=254, unique=True),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.CharField(choices=[('admin', 'Admin'), ('branch_head', 'Branch Head'), ('advisor', 'Advisor')], default='advisor', max_length=12),
        ),
    ]
