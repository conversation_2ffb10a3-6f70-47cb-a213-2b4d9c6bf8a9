from django.db import models
from django.conf import settings

class Store(models.Model):
    store_name = models.CharField(max_length=100)
    address = models.CharField(max_length=255, blank=True)
    code = models.CharField(max_length=10, unique=True)
    is_active = models.BooleanField(default=True)
    deleted = models.BooleanField(default=False)
    branch_head = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        null=True, blank=True,
        on_delete=models.SET_NULL,
        related_name='headed_store',
        limit_choices_to={'role': 'branch_head', 'deleted': False, 'is_active': True},
    )

    def __str__(self):
        return f"{self.store_name} - {self.code}"